import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';

class CodeInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final bool isLoading;
  final VoidCallback onActivate;

  const CodeInputWidget({
    super.key,
    required this.controller,
    required this.isLoading,
    required this.onActivate,
  });

  @override
  State<CodeInputWidget> createState() => _CodeInputWidgetState();
}

class _CodeInputWidgetState extends State<CodeInputWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isCodeValid = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    widget.controller.addListener(_validateCode);
  }

  @override
  void dispose() {
    _animationController.dispose();
    widget.controller.removeListener(_validateCode);
    super.dispose();
  }

  void _validateCode() {
    final code = widget.controller.text.trim();
    final isValid = code.length >= 6; // الحد الأدنى لطول الكود

    if (isValid != _isCodeValid) {
      setState(() {
        _isCodeValid = isValid;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              Colors.white,
              AppTheme.primaryColor.withValues(alpha: 0.02),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.vpn_key,
                    color: AppTheme.primaryColor,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Text(
                  'أدخل كود التفعيل',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),

            SizedBox(height: 8.h),

            Text(
              'أدخل الكود الذي حصلت عليه لتفعيل المواد',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),

            SizedBox(height: 24.h),

            // حقل إدخال الكود
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: widget.controller,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 2.0,
                  color: AppTheme.textPrimaryColor,
                ),
                inputFormatters: [
                  UpperCaseTextFormatter(),
                  FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
                  LengthLimitingTextInputFormatter(20),
                ],
                decoration: InputDecoration(
                  hintText: 'XXXXXX',
                  hintStyle: TextStyle(
                    color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
                    letterSpacing: 2.0,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: AppTheme.dividerColor,
                      width: 2,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: _isCodeValid
                          ? AppTheme.successColor
                          : AppTheme.dividerColor,
                      width: 2,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: AppTheme.primaryColor,
                      width: 2,
                    ),
                  ),
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: _isCodeValid
                        ? AppTheme.successColor
                        : AppTheme.textSecondaryColor,
                  ),
                  suffixIcon: _isCodeValid
                      ? Icon(Icons.check_circle, color: AppTheme.successColor)
                      : null,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 16.h,
                  ),
                ),
              ),
            ),

            SizedBox(height: 24.h),

            // زر التفعيل
            AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    width: double.infinity,
                    height: 56.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      gradient: _isCodeValid && !widget.isLoading
                          ? AppTheme.primaryGradient
                          : LinearGradient(
                              colors: [
                                AppTheme.textSecondaryColor.withValues(
                                  alpha: 0.3,
                                ),
                                AppTheme.textSecondaryColor.withValues(
                                  alpha: 0.2,
                                ),
                              ],
                            ),
                      boxShadow: _isCodeValid && !widget.isLoading
                          ? [
                              BoxShadow(
                                color: AppTheme.primaryColor.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ]
                          : [],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _isCodeValid && !widget.isLoading
                            ? () {
                                _animationController.forward().then((_) {
                                  _animationController.reverse();
                                });
                                widget.onActivate();
                              }
                            : null,
                        borderRadius: BorderRadius.circular(12.r),
                        child: Center(
                          child: widget.isLoading
                              ? SizedBox(
                                  width: 24.w,
                                  height: 24.h,
                                  child: const CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.rocket_launch,
                                      color: Colors.white,
                                      size: 20.sp,
                                    ),
                                    SizedBox(width: 8.w),
                                    Text(
                                      'تفعيل الاشتراك',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),

            SizedBox(height: 16.h),

            // مؤشر قوة الكود
            _buildCodeStrengthIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildCodeStrengthIndicator() {
    final code = widget.controller.text.trim();
    final strength = _calculateCodeStrength(code);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.security,
              size: 16.sp,
              color: AppTheme.textSecondaryColor,
            ),
            SizedBox(width: 8.w),
            Text(
              'قوة الكود',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        LinearProgressIndicator(
          value: strength,
          backgroundColor: AppTheme.dividerColor,
          valueColor: AlwaysStoppedAnimation<Color>(
            strength < 0.3
                ? AppTheme.errorColor
                : strength < 0.7
                ? AppTheme.warningColor
                : AppTheme.successColor,
          ),
        ),
      ],
    );
  }

  double _calculateCodeStrength(String code) {
    if (code.isEmpty) return 0.0;

    double strength = 0.0;

    // طول الكود
    if (code.length >= 6) strength += 0.3;
    if (code.length >= 8) strength += 0.2;
    if (code.length >= 10) strength += 0.2;

    // وجود أرقام
    if (code.contains(RegExp(r'[0-9]'))) strength += 0.15;

    // وجود حروف
    if (code.contains(RegExp(r'[A-Z]'))) strength += 0.15;

    return strength.clamp(0.0, 1.0);
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
