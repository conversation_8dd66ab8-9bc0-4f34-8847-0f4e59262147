import 'package:flutter/foundation.dart';

import '../models/unit_model.dart';
import '../models/question_model.dart';
import 'content_service.dart';
import 'exam_service.dart';
import 'offline_storage_service.dart';

/// خدمة تحميل المحتوى للعمل بدون إنترنت
class ContentDownloadService extends ChangeNotifier {
  static final ContentDownloadService _instance =
      ContentDownloadService._internal();
  static ContentDownloadService get instance => _instance;
  ContentDownloadService._internal();

  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _currentDownloadItem = '';
  String? _downloadError;

  bool get isDownloading => _isDownloading;
  double get downloadProgress => _downloadProgress;
  String get currentDownloadItem => _currentDownloadItem;
  String? get downloadError => _downloadError;

  /// تحميل محتوى مادة كاملة
  Future<bool> downloadSubjectContent(String subjectId) async {
    try {
      _setDownloading(true);
      _setProgress(0.0, 'بدء التحميل...');
      _clearError();

      // 1. تحميل معلومات المادة
      _setProgress(0.1, 'تحميل معلومات المادة...');
      final subject = await ContentService.instance.getSubjectById(subjectId);
      if (subject == null) {
        throw Exception('لم يتم العثور على المادة');
      }

      // 2. تحميل الوحدات
      _setProgress(0.2, 'تحميل الوحدات...');
      final units = await ContentService.instance.getSubjectUnits(subjectId);

      // 3. تحميل الدروس
      _setProgress(0.4, 'تحميل الدروس...');
      List<Lesson> allLessons = [];
      for (int i = 0; i < units.length; i++) {
        final lessons = await ContentService.instance.getUnitLessons(
          units[i].id,
        );
        allLessons.addAll(lessons);
        _setProgress(
          0.4 + (0.2 * (i + 1) / units.length),
          'تحميل دروس ${units[i].name}...',
        );
      }

      // 4. تحميل الأسئلة العادية
      _setProgress(0.6, 'تحميل الأسئلة العادية...');
      List<Question> allQuestions = [];

      // أسئلة الوحدات
      for (int i = 0; i < units.length; i++) {
        try {
          final unitQuestions = await ExamService.instance.getQuestionsByUnit(
            units[i].id,
            false,
          );
          allQuestions.addAll(unitQuestions);
        } catch (e) {
          // تجاهل الأخطاء في تحميل أسئلة وحدة معينة
        }
        _setProgress(
          0.6 + (0.1 * (i + 1) / units.length),
          'تحميل أسئلة ${units[i].name}...',
        );
      }

      // أسئلة الدروس
      _setProgress(0.7, 'تحميل أسئلة الدروس...');
      for (int i = 0; i < allLessons.length; i++) {
        try {
          final lessonQuestions = await ExamService.instance
              .getQuestionsByLesson(allLessons[i].id, false);
          allQuestions.addAll(lessonQuestions);
        } catch (e) {
          // تجاهل الأخطاء في تحميل أسئلة درس معين
        }
        _setProgress(
          0.7 + (0.1 * (i + 1) / allLessons.length),
          'تحميل أسئلة ${allLessons[i].name}...',
        );
      }

      // 5. تحميل أسئلة الدورات
      _setProgress(0.8, 'تحميل أسئلة الدورات...');
      for (int i = 0; i < units.length; i++) {
        try {
          final courseQuestions = await ExamService.instance.getQuestionsByUnit(
            units[i].id,
            true,
          );
          allQuestions.addAll(courseQuestions);
        } catch (e) {
          // تجاهل الأخطاء في تحميل أسئلة دورة معينة
        }
        _setProgress(
          0.8 + (0.1 * (i + 1) / units.length),
          'تحميل دورات ${units[i].name}...',
        );
      }

      // 6. حفظ البيانات محلياً
      _setProgress(0.9, 'حفظ البيانات محلياً...');

      // حفظ المادة
      final existingSubjects = await OfflineStorageService.instance
          .getOfflineSubjects();
      final updatedSubjects = existingSubjects
          .where((s) => s.id != subjectId)
          .toList();
      updatedSubjects.add(subject);
      await OfflineStorageService.instance.saveSubjects(updatedSubjects);

      // حفظ الوحدات
      final existingUnits = await OfflineStorageService.instance
          .getOfflineUnits();
      final updatedUnits = existingUnits
          .where((u) => u.subjectId != subjectId)
          .toList();
      updatedUnits.addAll(units);
      await OfflineStorageService.instance.saveUnits(updatedUnits);

      // حفظ الدروس
      final unitIds = units.map((u) => u.id).toList();
      final existingLessons = await OfflineStorageService.instance
          .getOfflineLessons();
      final updatedLessons = existingLessons
          .where((l) => !unitIds.contains(l.unitId))
          .toList();
      updatedLessons.addAll(allLessons);
      await OfflineStorageService.instance.saveLessons(updatedLessons);

      // حفظ الأسئلة
      final existingQuestions = await OfflineStorageService.instance
          .getOfflineQuestions();
      final updatedQuestions = existingQuestions
          .where((q) => q.subjectId != subjectId)
          .toList();
      updatedQuestions.addAll(allQuestions);
      await OfflineStorageService.instance.saveQuestions(updatedQuestions);

      // إضافة المادة لقائمة المواد المحملة
      await OfflineStorageService.instance.addDownloadedSubject(subjectId);
      await OfflineStorageService.instance.updateLastSyncTime();

      _setProgress(1.0, 'تم التحميل بنجاح!');

      // إخفاء شريط التقدم بعد ثانيتين
      await Future.delayed(const Duration(seconds: 2));
      _setDownloading(false);

      return true;
    } catch (e) {
      _setError('خطأ في التحميل: $e');
      _setDownloading(false);
      return false;
    }
  }

  /// حذف محتوى مادة من التخزين المحلي
  Future<bool> deleteSubjectContent(String subjectId) async {
    try {
      await OfflineStorageService.instance.clearSubjectData(subjectId);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في حذف المحتوى: $e');
      return false;
    }
  }

  /// تحديث محتوى مادة
  Future<bool> updateSubjectContent(String subjectId) async {
    return await downloadSubjectContent(subjectId);
  }

  /// فحص ما إذا كانت المادة محملة
  Future<bool> isSubjectDownloaded(String subjectId) async {
    return await OfflineStorageService.instance.isSubjectDownloaded(subjectId);
  }

  /// الحصول على قائمة المواد المحملة
  Future<List<String>> getDownloadedSubjects() async {
    return await OfflineStorageService.instance.getDownloadedSubjects();
  }

  /// الحصول على إحصائيات التخزين
  Future<Map<String, dynamic>> getStorageStats() async {
    return await OfflineStorageService.instance.getOfflineStats();
  }

  /// مسح جميع البيانات المحملة
  Future<bool> clearAllDownloadedContent() async {
    try {
      await OfflineStorageService.instance.clearAllOfflineData();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في مسح البيانات: $e');
      return false;
    }
  }

  void _setDownloading(bool downloading) {
    _isDownloading = downloading;
    notifyListeners();
  }

  void _setProgress(double progress, String item) {
    _downloadProgress = progress;
    _currentDownloadItem = item;
    notifyListeners();
  }

  void _setError(String error) {
    _downloadError = error;
    notifyListeners();
  }

  void _clearError() {
    _downloadError = null;
    notifyListeners();
  }
}
