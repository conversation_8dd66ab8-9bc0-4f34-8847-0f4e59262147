import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/content_service.dart';
import 'units_lessons_management_page.dart';

class SubjectsManagementPage extends StatefulWidget {
  const SubjectsManagementPage({super.key});

  @override
  State<SubjectsManagementPage> createState() => _SubjectsManagementPageState();
}

class _SubjectsManagementPageState extends State<SubjectsManagementPage> {
  List<Subject> _subjects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSubjects();
  }

  Future<void> _loadSubjects() async {
    try {
      final subjects = await ContentService.instance.getAllSubjects();
      setState(() {
        _subjects = subjects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المواد: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إدارة المواد',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showAddSubjectDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إضافة مادة جديدة',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _subjects.isEmpty
          ? _buildEmptyState()
          : RefreshIndicator(
              onRefresh: _loadSubjects,
              child: ListView.builder(
                padding: EdgeInsets.all(16.w),
                itemCount: _subjects.length,
                itemBuilder: (context, index) {
                  final subject = _subjects[index];
                  return _buildSubjectCard(subject);
                },
              ),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على + لإضافة مادة جديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    final color = Color(int.parse(subject.color.replaceFirst('#', '0xFF')));

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة المادة
                  Container(
                    width: 50.w,
                    height: 50.h,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(Icons.book, color: Colors.white, size: 24.sp),
                  ),

                  SizedBox(width: 16.w),

                  // معلومات المادة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subject.name,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          subject.description,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // حالة التفعيل
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: subject.isActive
                          ? AppTheme.successColor.withValues(alpha: 0.1)
                          : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      subject.isActive ? 'مفعل' : 'معطل',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: subject.isActive
                            ? AppTheme.successColor
                            : AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                UnitsLessonsManagementPage(subject: subject),
                          ),
                        );
                      },
                      icon: Icon(Icons.folder, size: 16.sp),
                      label: Text('الوحدات والدروس'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: color.withValues(alpha: 0.1),
                        foregroundColor: color,
                        elevation: 0,
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  IconButton(
                    onPressed: () => _showEditSubjectDialog(subject),
                    icon: Icon(Icons.edit, color: AppTheme.primaryColor),
                    tooltip: 'تعديل',
                  ),

                  IconButton(
                    onPressed: () => _toggleSubjectStatus(subject),
                    icon: Icon(
                      subject.isActive
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: subject.isActive
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                    ),
                    tooltip: subject.isActive ? 'إلغاء تفعيل' : 'تفعيل',
                  ),

                  IconButton(
                    onPressed: () => _deleteSubject(subject),
                    icon: Icon(Icons.delete, color: AppTheme.errorColor),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddSubjectDialog() {
    _showSubjectDialog();
  }

  void _showEditSubjectDialog(Subject subject) {
    _showSubjectDialog(subject: subject);
  }

  void _showSubjectDialog({Subject? subject}) {
    final isEditing = subject != null;
    final nameController = TextEditingController(text: subject?.name ?? '');
    final descriptionController = TextEditingController(
      text: subject?.description ?? '',
    );
    Color selectedColor = subject != null
        ? Color(int.parse(subject.color.replaceFirst('#', '0xFF')))
        : AppTheme.primaryColor;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل المادة' : 'إضافة مادة جديدة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المادة',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'وصف المادة',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  'اختر لون المادة:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 8.h),
                Wrap(
                  spacing: 8.w,
                  children:
                      [
                            AppTheme.primaryColor,
                            AppTheme.secondaryColor,
                            AppTheme.accentColor,
                            AppTheme.successColor,
                            AppTheme.warningColor,
                            AppTheme.errorColor,
                          ]
                          .map(
                            (color) => GestureDetector(
                              onTap: () {
                                setDialogState(() {
                                  selectedColor = color;
                                });
                              },
                              child: Container(
                                width: 40.w,
                                height: 40.h,
                                decoration: BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                  border: selectedColor == color
                                      ? Border.all(
                                          color: Colors.black,
                                          width: 3,
                                        )
                                      : null,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('يرجى إدخال اسم المادة')),
                  );
                  return;
                }

                try {
                  final colorHex =
                      '#${selectedColor.toARGB32().toRadixString(16).substring(2)}';

                  if (isEditing) {
                    final updatedSubject = subject.copyWith(
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      color: colorHex,
                    );
                    await ContentService.instance.updateSubject(updatedSubject);
                  } else {
                    final newSubject = Subject(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      iconUrl: '',
                      color: colorHex,
                      isActive: true,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    );
                    await ContentService.instance.addSubject(newSubject);
                  }

                  if (mounted && context.mounted) {
                    Navigator.pop(context);
                    _loadSubjects();

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isEditing
                              ? 'تم تحديث المادة بنجاح'
                              : 'تم إضافة المادة بنجاح',
                        ),
                        backgroundColor: AppTheme.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
                  }
                }
              },
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleSubjectStatus(Subject subject) async {
    try {
      final updatedSubject = subject.copyWith(isActive: !subject.isActive);
      await ContentService.instance.updateSubject(updatedSubject);
      _loadSubjects();

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              subject.isActive ? 'تم إلغاء تفعيل المادة' : 'تم تفعيل المادة',
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
      }
    }
  }

  Future<void> _deleteSubject(Subject subject) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف مادة "${subject.name}"؟\nسيتم حذف جميع الوحدات والدروس والأسئلة المرتبطة بها.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ContentService.instance.deleteSubject(subject.id);
        _loadSubjects();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف المادة بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }
}
